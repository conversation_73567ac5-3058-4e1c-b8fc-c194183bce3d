<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Desktop Player</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header with controls -->
    <header class="header">
        <div class="header-left">
            <button id="backBtn" class="control-btn" title="Go Back">
                <i class="fas fa-arrow-left"></i>
            </button>
            <button id="forwardBtn" class="control-btn" title="Go Forward">
                <i class="fas fa-arrow-right"></i>
            </button>
            <button id="reloadBtn" class="control-btn" title="Reload">
                <i class="fas fa-redo"></i>
            </button>
        </div>
        
        <div class="header-center">
            <div class="url-bar">
                <input type="text" id="urlInput" placeholder="Enter YouTube URL or search...">
                <button id="goBtn" class="go-btn">
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
        
        <div class="header-right">
            <button id="themeToggleBtn" class="control-btn" title="Toggle Theme">
                <i class="fas fa-moon"></i>
            </button>
            <button id="settingsBtn" class="control-btn" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
            <button id="fullscreenBtn" class="control-btn" title="Toggle Fullscreen">
                <i class="fas fa-expand"></i>
            </button>
        </div>
    </header>

    <!-- Main content area -->
    <main class="main-content">
        <webview id="webview" src="https://www.youtube.com" allowpopups webpreferences="javascript=yes,plugins=yes"></webview>
    </main>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <button id="closeSettingsBtn" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Video Quality</h3>
                    <div class="quality-controls">
                        <label class="quality-option">
                            <input type="radio" name="quality" value="auto" id="quality-auto">
                            <span class="quality-label">Auto</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="144p" id="quality-144p">
                            <span class="quality-label">144p</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="240p" id="quality-240p">
                            <span class="quality-label">240p</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="360p" id="quality-360p">
                            <span class="quality-label">360p</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="480p" id="quality-480p">
                            <span class="quality-label">480p</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="720p" id="quality-720p">
                            <span class="quality-label">720p</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="1080p" id="quality-1080p">
                            <span class="quality-label">1080p</span>
                        </label>
                        <label class="quality-option">
                            <input type="radio" name="quality" value="4k" id="quality-4k">
                            <span class="quality-label">4K</span>
                        </label>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Player Settings</h3>
                    <div class="setting-item">
                        <label for="autoplay">Autoplay</label>
                        <input type="checkbox" id="autoplay" name="autoplay">
                    </div>
                    <div class="setting-item">
                        <label for="volume">Default Volume</label>
                        <input type="range" id="volume" name="volume" min="0" max="100" value="100">
                        <span id="volumeValue">100%</span>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Appearance</h3>
                    <div class="setting-item">
                        <label for="theme">Theme</label>
                        <select id="theme" name="theme">
                            <option value="dark">Dark</option>
                            <option value="light">Light</option>
                            <option value="auto">Auto</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="language">Language</label>
                        <select id="language" name="language">
                            <option value="ar">العربية</option>
                            <option value="en">English</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <button id="saveSettingsBtn" class="btn btn-primary">Save Settings</button>
                <button id="cancelSettingsBtn" class="btn btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Quality Control Overlay -->
    <div id="qualityOverlay" class="quality-overlay">
        <div class="quality-panel">
            <h3>Video Quality</h3>
            <div class="quality-buttons">
                <button class="quality-btn" data-quality="auto">Auto</button>
                <button class="quality-btn" data-quality="144p">144p</button>
                <button class="quality-btn" data-quality="240p">240p</button>
                <button class="quality-btn" data-quality="360p">360p</button>
                <button class="quality-btn" data-quality="480p">480p</button>
                <button class="quality-btn" data-quality="720p">720p</button>
                <button class="quality-btn" data-quality="1080p">1080p</button>
                <button class="quality-btn" data-quality="4k">4K</button>
            </div>
            <button id="closeQualityBtn" class="close-quality-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Loading indicator -->
    <div id="loadingIndicator" class="loading-indicator">
        <div class="spinner"></div>
        <span>Loading...</span>
    </div>

    <script src="renderer.js"></script>
</body>
</html>